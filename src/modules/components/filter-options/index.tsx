import { AssetsSetting } from "@/assets/icons";
import { SelectGroups, SQBtn, SquareBtnGroup } from "@dilpesh/kgk-ui-library";
import { listViewOptions, sortOptions } from "../../utils/constant";

type Props = {
    setActivebtn: any
    activeBtn: string
    setasettingModalOpen: any
    setSortValue: any
    sortValue: string
}

const FilterOptions = (props: Props) => {
    return (
        <div className="custom_managewrap">
            <div className="select_wprs">
                <div className="sort_by_box">
                    <p>Sort by</p>
                    <SelectGroups
                        options={sortOptions}
                        defaultValue={sortOptions}
                        dropdownStyleWidth={300}
                        onChange={(e: any) => {
                            props?.setSortValue(e);
                        }}
                        value={props?.sortValue}

                    // open={sortOpen}
                    // close={() => { setSortOpen(false) }}
                    />                </div>
            </div>
            <SquareBtnGroup
                data={listViewOptions}
                value={props?.activeBtn}
                onChange={(e: any) => props?.setActivebtn(e.target.value)}
            />
            <div className="setting_wrap">
                <SQBtn bg="border" onClick={() => { props?.setasettingModalOpen(true) }}><AssetsSetting /></SQBtn>
            </div>
        </div>
    )
};

export default FilterOptions;
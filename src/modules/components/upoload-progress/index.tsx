import { DownArrowIcon, FileListGallery } from "@/assets/icons";
import { CloseIcon } from "@magneto-it-solutions/kgk-common-library";
import { Progress } from "antd";
import { useEffect, useState } from "react";

interface UploadingFile {
    file: File;
    progress: number;
    status: 'uploading' | 'completed' | 'error';
    id: string;
}

type Props = {
    leftPosition: string;
    uploadingFiles?: UploadingFile[];
    isUploading?: boolean;
    onCancelUpload?: (fileId: string) => void;
    onClearCompleted?: () => void;
}

const UploadProgress = (props: Props) => {
    const [uploadingItem, setuploadingItem] = useState(false);
    const [hoveredFileId, setHoveredFileId] = useState<string | null>(null);

    const { uploadingFiles = [], isUploading = false, onCancelUpload, onClearCompleted } = props;

    // Auto-expand when uploading starts
    useEffect(() => {
        if (isUploading && uploadingFiles.length > 0) {
            setuploadingItem(true);
        }
    }, [isUploading, uploadingFiles.length]);

    const handleUploadToggle = () => {
        setuploadingItem((current) => !current);
    };

    const handleCancelFile = (fileId: string) => {
        if (onCancelUpload) {
            onCancelUpload(fileId);
        }
    };

    const completedFiles = uploadingFiles.filter(f => f.status === 'completed').length;
    const totalFiles = uploadingFiles.length;

    // Don't show component if no files are uploading or uploaded
    if (totalFiles === 0) {
        return null;
    }

    return (
        <div className="uploading_wpr" style={{ right: props?.leftPosition }}>
            <div className={`heading_wpr ${uploadingItem ? 'active' : ''}`}>
                <p>
                    {isUploading
                        ? `Uploading ${totalFiles} item${totalFiles > 1 ? 's' : ''}`
                        : `Uploaded ${completedFiles} of ${totalFiles} item${totalFiles > 1 ? 's' : ''}`
                    }
                </p>
                <div className="action_wpr">
                    <DownArrowIcon onClick={handleUploadToggle} className="arrow" />
                    {!isUploading && onClearCompleted && (
                        <div onClick={onClearCompleted} style={{ cursor: 'pointer' }}>
                            <CloseIcon />
                        </div>
                    )}
                </div>
            </div>
            <div className={`innerdata_wpr ${uploadingItem ? 'active' : 'close'}`}>
                {uploadingFiles.map((uploadFile) => (
                    <div
                        key={uploadFile.id}
                        className="item_box"
                        onMouseEnter={() => setHoveredFileId(uploadFile.id)}
                        onMouseLeave={() => setHoveredFileId(null)}
                    >
                        <div className="text_wrap">
                            <FileListGallery />
                            <p>{uploadFile.file.name}</p>
                        </div>
                        <div className="progress_wpr">
                            {hoveredFileId === uploadFile.id && uploadFile.status === 'uploading' ? (
                                <div onClick={() => handleCancelFile(uploadFile.id)} style={{ cursor: 'pointer' }}>
                                    <CloseIcon />
                                </div>
                            ) : (
                                <Progress
                                    type="circle"
                                    strokeWidth={16}
                                    percent={uploadFile.progress}
                                    size={24}
                                    showInfo={false}
                                    trailColor="#C5C5C5"
                                    strokeColor={
                                        uploadFile.status === 'completed' ? '#52c41a' :
                                            uploadFile.status === 'error' ? '#ff4d4f' :
                                                '#383230'
                                    }
                                />
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )
};

export default UploadProgress;
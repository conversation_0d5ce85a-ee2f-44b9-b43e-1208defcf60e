"use client"
import { CopyIcon, DeleteIcon, DownloadIcon, FolderInfo, InfoIcon, LinkIcon, MoveIcon, ShareIcon } from "@/assets/icons";
import { ActionDropdown } from "@dilpesh/kgk-ui-library";
import { CloseIcon } from "@magneto-it-solutions/kgk-common-library";
import { Link } from "@mui/material";
import { itemsForList, superActionListFile } from "../../utils/constant";

type Props = {}

const Selectedheader = (props: Props) => {
    return (
        <div className="selection_panel">
            <div className="item_selected">
                <CloseIcon />
                <p>1 selected</p>
            </div>
            <div className="other_actions">
                <Link href="javascript:void(0)"><DownloadIcon /></Link>
                <Link href="javascript:void(0)"><CopyIcon /></Link>
                <Link href="javascript:void(0)"><ShareIcon /></Link>
                <Link href="javascript:void(0)"><LinkIcon /></Link>
                <Link href="javascript:void(0)"><MoveIcon /></Link>
                <Link href="javascript:void(0)"><DeleteIcon /></Link>
                <ActionDropdown
                    items={itemsForList}
                    actionList={superActionListFile}
                    className="info_action"
                    actionIcon={<InfoIcon />}
                />
            </div>
            <ActionDropdown
                items={itemsForList}
                actionList={superActionListFile}
                className="info_action"
                actionIcon={<FolderInfo />}
                overlayClassName="icon_dropdown"
            />
        </div>
    )
};

export default Selectedheader;
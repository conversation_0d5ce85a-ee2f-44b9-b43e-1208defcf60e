"use client"
import React, { createContext, useContext, ReactNode } from 'react';
import { DragDropContext as BeautifulDragDropContext, DropResult } from 'react-beautiful-dnd';
import { useMoveAssets } from '@magneto-it-solutions/kgk-common-library';

interface DragDropContextType {
  onDragEnd: (result: DropResult) => void;
  isMoving: boolean;
}

interface DragDropProviderProps {
  children: ReactNode;
  onMoveSuccess?: () => void;
  currentFolderId?: string;
}

const DragDropContextProvider = createContext<DragDropContextType | undefined>(undefined);

export const useDragDropContext = () => {
  const context = useContext(DragDropContextProvider);
  if (!context) {
    throw new Error('useDragDropContext must be used within a DragDropProvider');
  }
  return context;
};

export const DragDropProvider: React.FC<DragDropProviderProps> = ({
  children,
  onMoveSuccess,
  currentFolderId = ''
}) => {
  const { moveAssets, isLoading: isMoving } = useMoveAssets();

  const onDragEnd = async (result: DropResult) => {
    const { destination, source, draggableId } = result;

    // If dropped outside a valid drop zone
    if (!destination) {
      return;
    }

    // If dropped in the same position
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    try {
      // Parse the draggable ID to get item info
      const [itemType, itemId] = draggableId.split('-');
      
      // Determine destination folder ID
      let destinationFolderId: string | null = null;
      
      if (destination.droppableId.startsWith('folder-')) {
        // Dropped on a specific folder
        destinationFolderId = destination.droppableId.replace('folder-', '');
      } else if (destination.droppableId === 'current-folder') {
        // Dropped on current folder area
        destinationFolderId = currentFolderId || null;
      }

      // Validate that we're not moving a folder into itself
      if (itemType === 'folder' && itemId === destinationFolderId) {
        console.log('Cannot move folder into itself');
        return;
      }

      // Perform the move operation
      await moveAssets({
        asset_id: itemId,
        parent_folder_id: destinationFolderId,
        asset_type: itemType === 'file' ? 'file' : 'folder',
      });

      // Call success callback
      onMoveSuccess?.();
      
    } catch (error) {
      console.error('Error moving item:', error);
    }
  };

  const contextValue: DragDropContextType = {
    onDragEnd,
    isMoving
  };

  return (
    <DragDropContextProvider.Provider value={contextValue}>
      <BeautifulDragDropContext onDragEnd={onDragEnd}>
        {children}
      </BeautifulDragDropContext>
    </DragDropContextProvider.Provider>
  );
};

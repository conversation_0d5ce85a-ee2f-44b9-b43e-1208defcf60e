/* Drag and Drop Styles */

.draggable-item {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &.dragging {
    z-index: 1000;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transform: rotate(5deg) !important;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.droppable-area {
  transition: all 0.2s ease;
  position: relative;
  
  &.drag-over {
    background-color: rgba(24, 144, 255, 0.1) !important;
    border: 2px dashed #1890ff !important;
    border-radius: 8px;
  }
  
  &.drag-active {
    transform: scale(1.02);
  }
}

.folder-drop-zone {
  min-height: 80px;
  border-radius: 8px;
  
  &.drag-over {
    background-color: rgba(24, 144, 255, 0.15) !important;
    border: 2px dashed #1890ff !important;
    
    .icon24_bes_box {
      transform: scale(1.05);
    }
  }
}

.grid-view-container {
  min-height: 200px;
  padding: 16px;
  border-radius: 8px;
  
  &.drag-over {
    background-color: rgba(24, 144, 255, 0.05) !important;
    border: 2px dashed #1890ff !important;
  }
}

/* Drop indicator styles */
.drop-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  color: #1890ff;
  font-weight: bold;
  font-size: 14px;
  z-index: 1000;
  background-color: white;
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #1890ff;
}

/* Drag handle styles */
.drag-handle {
  cursor: grab;
  
  &:active {
    cursor: grabbing;
  }
}

/* Disable text selection during drag */
.dragging * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Folder hover effects */
.folder-drop-zone:hover {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
}

/* Animation for successful drop */
@keyframes dropSuccess {
  0% {
    transform: scale(1);
    background-color: rgba(82, 196, 26, 0.1);
  }
  50% {
    transform: scale(1.05);
    background-color: rgba(82, 196, 26, 0.2);
  }
  100% {
    transform: scale(1);
    background-color: transparent;
  }
}

.drop-success {
  animation: dropSuccess 0.6s ease-out;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .draggable-item {
    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
  
  .droppable-area {
    &.drag-over {
      border-width: 3px;
    }
  }
  
  .drop-indicator {
    font-size: 12px;
    padding: 6px 10px;
  }
}

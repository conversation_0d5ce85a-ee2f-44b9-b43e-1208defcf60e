"use client"
import React, { ReactNode } from 'react';
import { Droppable } from 'react-beautiful-dnd';

interface DroppableAreaProps {
  children: ReactNode;
  droppableId: string;
  type?: string;
  isDropDisabled?: boolean;
  className?: string;
}

export const DroppableArea: React.FC<DroppableAreaProps> = ({
  children,
  droppableId,
  type = 'DEFAULT',
  isDropDisabled = false,
  className = ''
}) => {
  return (
    <Droppable 
      droppableId={droppableId} 
      type={type}
      isDropDisabled={isDropDisabled}
    >
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.droppableProps}
          className={`droppable-area ${className} ${
            snapshot.isDraggingOver ? 'drag-over' : ''
          } ${snapshot.draggingOverWith ? 'drag-active' : ''}`}
          style={{
            backgroundColor: snapshot.isDraggingOver ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
            border: snapshot.isDraggingOver ? '2px dashed #1890ff' : '2px solid transparent',
            borderRadius: '8px',
            transition: 'all 0.2s ease',
            minHeight: '50px',
            position: 'relative'
          }}
        >
          {children}
          {provided.placeholder}
          
          {/* Drop indicator */}
          {snapshot.isDraggingOver && (
            <div
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                pointerEvents: 'none',
                color: '#1890ff',
                fontWeight: 'bold',
                fontSize: '14px',
                zIndex: 1000,
                backgroundColor: 'white',
                padding: '4px 8px',
                borderRadius: '4px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}
            >
              Drop here to move
            </div>
          )}
        </div>
      )}
    </Droppable>
  );
};

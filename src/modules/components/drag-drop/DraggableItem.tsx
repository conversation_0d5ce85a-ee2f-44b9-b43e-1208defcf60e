"use client"
import React, { ReactNode } from 'react';
import { Draggable } from 'react-beautiful-dnd';

interface DraggableItemProps {
  children: ReactNode;
  draggableId: string;
  index: number;
  isDragDisabled?: boolean;
}

export const DraggableItem: React.FC<DraggableItemProps> = ({
  children,
  draggableId,
  index,
  isDragDisabled = false
}) => {
  return (
    <Draggable 
      draggableId={draggableId} 
      index={index}
      isDragDisabled={isDragDisabled}
    >
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          style={{
            ...provided.draggableProps.style,
            opacity: snapshot.isDragging ? 0.8 : 1,
            transform: snapshot.isDragging 
              ? `${provided.draggableProps.style?.transform} rotate(5deg)` 
              : provided.draggableProps.style?.transform,
            transition: snapshot.isDragging ? 'none' : 'transform 0.2s ease',
            cursor: isDragDisabled ? 'default' : 'grab',
            userSelect: 'none'
          }}
          className={`draggable-item ${snapshot.isDragging ? 'dragging' : ''}`}
        >
          {children}
        </div>
      )}
    </Draggable>
  );
};

import React from "react";
import { type ActionHandlers, type FolderActionHandlers } from "../../utils/actionHandlers";
import GridView from "./grid-view";
import ListView from "./list-view";

interface Folder {
    _id: string;
    folder_name: string;
    folder_slug: string;
    parent_folder_id: string | null;
    createdAt: string;
    updatedAt: string | null;
    is_public: boolean;
}

interface FoldersFilesProps {
    viewMode: string;
    folderData: any;
    selectedItems: string[];
    onFolderNavigate: (folder: Folder) => void;
    onFileInformation?: (file: File) => void;
    onItemSelect: (itemId: string) => void;
    fileActionHandlers?: ActionHandlers;
    folderActionHandlers?: FolderActionHandlers;
}

const FoldersFiles: React.FC<FoldersFilesProps> = ({
    viewMode,
    folderData,
    selectedItems,
    onFolderNavigate,
    onFileInformation,
    onItemSelect,
    fileActionHandlers,
    folderActionHandlers
}) => {
    const folders = folderData?.folders || [];
    const files = folderData?.files || [];

    const handleFolderClick = (folder: Folder) => {
        onFolderNavigate(folder);
    };

    const handleFileClick = (file: any) => {
        window.open(process.env.NEXT_PUBLIC_ASSETS_URL + file.path, '_blank');
    };

    const handleItemSelect = (itemId: string) => {
        onItemSelect(itemId);
    };

    return (
        <div className={`datacontent_wrap ${viewMode}`}>
            {viewMode === "grid" ? (
                <GridView
                    folders={folders}
                    files={files}
                    selectedItems={selectedItems}
                    onFolderClick={handleFolderClick}
                    onFileClick={handleFileClick}
                    onFileInformation={onFileInformation}
                    onItemSelect={handleItemSelect}
                    fileActionHandlers={fileActionHandlers}
                    folderActionHandlers={folderActionHandlers}
                />
            ) : (
                <ListView
                    folders={folders}
                    files={files}
                    onFolderClick={handleFolderClick}
                    onFileClick={handleFileClick}
                    onFileInformation={onFileInformation}
                    fileActionHandlers={fileActionHandlers}
                    folderActionHandlers={folderActionHandlers}
                />
            )}
        </div>
    );
};

export default FoldersFiles;
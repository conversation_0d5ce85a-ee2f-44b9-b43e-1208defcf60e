"use client"
import { FileListGallery, FolderInfo, FolderList } from "@/assets/icons";
import { ActionDropdown, OnlyCk } from "@dilpesh/kgk-ui-library";
import { Col, Row } from "antd";
import {
    createFileActionHandlers,
    createFileActionList,
    createFolderActionHandlers,
    createFolderActionList,
    type ActionHandlers,
    type FolderActionHandlers
} from "../../utils/actionHandlers";
import { itemsForList, superActionListFile, superActionListFolderWithShare } from "../../utils/constant";
import { DraggableItem, DroppableArea } from "../drag-drop";

interface Folder {
    _id: string;
    folder_name: string;
    folder_slug: string;
    parent_folder_id: string | null;
    createdAt: string;
    updatedAt: string | null;
    is_public: boolean;
}

interface GridViewProps {
    folders: any[];
    files: any[];
    selectedItems: string[];
    onFolderClick: (folder: any) => void;
    onFileClick: (file: File) => void;
    onFileInformation?: (file: any) => void;
    onItemSelect: (itemId: string) => void;
    fileActionHandlers?: ActionHandlers;
    folderActionHandlers?: FolderActionHandlers;
}

const GridView: React.FC<GridViewProps> = ({
    folders,
    files,
    selectedItems,
    onFolderClick,
    onFileClick,
    onFileInformation,
    onItemSelect,
    fileActionHandlers,
    folderActionHandlers
}) => {
    console.log('folders', folders);
    console.log('files', files);

    const handleFolderClick = (folder: Folder) => {
        onFolderClick(folder);
    };

    const handleFileClick = (file: any) => {
        onFileClick(file);
    };

    const handleItemSelect = (itemId: string) => {
        onItemSelect(itemId);
    };

    return (
        <DroppableArea droppableId="current-folder" className="grid-view-container">
            <div className="folder_wrap inner_section">
                <h2>Folders</h2>
                <Row className="folder_list" gutter={{ xs: 12, sm: 24 }}>
                    {folders.map((folder, index) => (
                        <Col key={folder._id} xs={12} md={8} lg={6} xxl={4}>
                            <DraggableItem
                                draggableId={`folder-${folder._id}`}
                                index={index}
                            >
                                <DroppableArea
                                    droppableId={`folder-${folder._id}`}
                                    className="folder-drop-zone"
                                >
                                    <div className="icon24_bes_box">
                                        <div
                                            onClick={() => handleFolderClick(folder)}
                                            className="inner_box"
                                            style={{ cursor: 'pointer' }}
                                        >
                                            <FolderList />
                                            <p>{folder.folder_name}</p>
                                        </div>
                                        <ActionDropdown
                                            items={itemsForList}
                                            actionList={folderActionHandlers ?
                                                createFolderActionList(folder, createFolderActionHandlers(folderActionHandlers), true) :
                                                superActionListFolderWithShare
                                            }
                                            className="info_action"
                                            actionIcon={<FolderInfo />}
                                        />
                                    </div>
                                </DroppableArea>
                            </DraggableItem>
                        </Col>
                    ))}
                </Row>
            </div>
            {files.length > 0 && (
                <div className="files_wrap inner_section">
                    <h2>Files</h2>
                    <Row className="files_list" gutter={{ xs: 12, sm: 24 }}>
                        {files.map((file: any, index: number) => {
                            const isSelected = selectedItems.includes(file._id);
                            const fileIndex = folders.length + index; // Offset by folder count for unique indices
                            return (
                                <Col key={file._id || index} xs={12} md={8} lg={6} xxl={4}>
                                    <DraggableItem
                                        draggableId={`file-${file._id}`}
                                        index={fileIndex}
                                    >
                                        <div className={`withmedia_file ${isSelected ? "selected" : ""}`}>
                                            <div className="item_detail">
                                                <div
                                                    className="inner_box"
                                                    style={{ cursor: 'pointer' }}
                                                >
                                                    <FileListGallery />
                                                    <OnlyCk
                                                        checked={isSelected}
                                                        onChange={() => { handleItemSelect(file._id) }}
                                                    />
                                                    <p onClick={() => handleFileClick(file)}
                                                    >{file.file_name || `File ${index + 1}`}</p>
                                                </div>
                                                <ActionDropdown
                                                    items={itemsForList}
                                                    actionList={fileActionHandlers ?
                                                        createFileActionList(file, createFileActionHandlers(fileActionHandlers)) :
                                                        superActionListFile
                                                    }
                                                    className="info_action"
                                                    actionIcon={<FolderInfo />}
                                                    overlayClassName="icon_dropdown"
                                                />
                                            </div>
                                            <div className="item_image">
                                                <img
                                                    src={process.env.NEXT_PUBLIC_ASSETS_URL + file.path || "/assets-crm/assets/img/FileImage1.png"}
                                                    alt={file.file_name || "File"}
                                                    height={100}
                                                    width={100}
                                                />
                                            </div>
                                        </div>
                                    </DraggableItem>
                                </Col>
                            );
                        })}
                    </Row>
                </div>
            )}
        </DroppableArea>
    )
};

export default GridView;
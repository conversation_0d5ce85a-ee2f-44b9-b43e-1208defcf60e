import { Media, bes, br, bs } from "@dilpesh/kgk-ui-library";
import { Modal } from "antd";
import styled, { css } from "styled-components";

export const AssetsSettingModalStyle = styled(Modal)`
    ${(props) =>
        css`
            .upper_storedata {
                background-color: ${(props) => props?.theme?.colors?.brand_low};
                padding: 16px 24px;
                border-radius: 4px;
                margin-bottom: 24px;
                @media ${Media.ultra_mobile}{
                    padding: 16px;
                }
                & > h3 {
                    ${br(props?.theme?.text?.high, 500)};
                    margin-bottom: 16px;
                    @media ${Media.ultra_mobile}{
                        margin-bottom: 12px;
                    }
                }
                & > p {
                    ${bes(props?.theme?.text?.high, 400)};
                    margin-top: 8px;
                }
                .progress_bar {
                    width: 100%;
                    background-color: ${(props) => props?.theme?.colors?.brand_mid};
                    border-radius: 24px;
                }
                .progress_bar_fill {
                    display: block;
                    height: 16px;
                    background-color: ${(props) => props?.theme?.colors?.brand_high};
                    border-radius: 24px;
                    transition: width 500ms ease-in-out;
                }
            }
            .mode_view {
                display: flex;
                align-items: center;
                & > h4 {
                    padding-right: 24px;
                    ${bs(props?.theme?.text?.high, 500)};
                    @media ${Media.ultra_mobile}{
                        padding-right: 16px;
                    }
                }
            }
        `
    }
`
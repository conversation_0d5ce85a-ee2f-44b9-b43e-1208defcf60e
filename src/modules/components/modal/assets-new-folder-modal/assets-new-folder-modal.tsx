import { Btn, InputGroups } from "@dilpesh/kgk-ui-library";
import { CloseIcon, useCreateFolder } from "@magneto-it-solutions/kgk-common-library";
import { Col, Row } from "antd";
import { useState } from "react";
import { AssetsNewFolderModalStyle } from "./assets-new-folder-modal.style";

interface AssetsNewFolderModalProps {
    show: boolean;
    onHide: () => void;
    rejectBtnEvent: () => void;
    currentFolderId?: string;
    onSuccess?: () => void;
}

export default function AssetsNewFolderModal({
    show,
    onHide,
    rejectBtnEvent,
    currentFolderId,
    onSuccess,
}: AssetsNewFolderModalProps) {
    const { createFolder, isLoading } = useCreateFolder();
    const [folderName, setFolderName] = useState('');
    const [error, setError] = useState('');

    const handleCreateFolder = async () => {
        if (!folderName.trim()) {
            setError('Folder name is required');
            return;
        }

        try {
            setError('');

            // Prepare the data according to the API interface
            const folderData = {
                folder_name: folderName.trim(),
                parent_folder_id: currentFolderId || '', // Use currentFolderId from URL, empty string if root
            };

            await createFolder(folderData);

            // Reset form
            setFolderName('');

            // Call success callback
            if (onSuccess) {
                onSuccess();
            }
        } catch (error) {
            console.error('Error creating folder:', error);
            setError('Failed to create folder. Please try again.');
        }
    };

    const handleCancel = () => {
        setFolderName('');
        setError('');
        rejectBtnEvent();
    };

    return (
        <AssetsNewFolderModalStyle
            title={'New folder'}
            centered
            closeIcon={<CloseIcon />}
            open={show}
            onOk={onHide}
            onCancel={onHide}
            width={464}
            wrapClassName="detail_modal"
            footer={[
                <Btn onClick={handleCancel} size="large" disabled={isLoading}>
                    Cancel
                </Btn>,
                <Btn
                    bg="fill"
                    size="large"
                    onClick={handleCreateFolder}
                    isloading={isLoading}
                    disabled={!folderName.trim() || isLoading}
                >
                    Create
                </Btn>,
            ]}
        >
            <form className="body" onSubmit={(e) => { e.preventDefault(); handleCreateFolder(); }}>
                <Row>
                    <Col xs={24}>
                        <InputGroups
                            label="Folder name"
                            value={folderName}
                            onChange={(e: any) => setFolderName(e.target.value)}
                            placeholder="Enter folder name"
                            disabled={isLoading}
                            error={error}
                        />
                    </Col>
                </Row>
            </form>
        </AssetsNewFolderModalStyle>
    )
}

import { Media, b2xs, bs } from "@dilpesh/kgk-ui-library";
import { Modal } from "antd";
import styled, { css } from "styled-components";

export const AssetsInfoModalStyle = styled(Modal)`
    ${(props) =>
        css`
        .ant-modal-content {
            .ant-modal-body {
                .inner_section_wrap {
                    padding-bottom: 32px;
                    @media ${Media.ultra_mobile}{
                        padding-bottom: 24px;
                    }
                    h3 {
                        ${bs(props?.theme?.text?.high, 500)};
                    }
                    .top_section {
                        .img_wpr{
                            border-radius: 4px;
                            overflow: hidden;
                        }
                        .access_users {
                            margin-top: 16px;
                            .image_wrap {
                                display: flex;
                                justify-content: start;
                                align-items: start;
                                margin: 8px 0;
                                svg, img {
                                    border-radius: 50%;
                                    height: 32px;
                                    width: 32px;
                                }
                                .shared_img {
                                    display: flex;
                                    padding-left: 8px;
                                    margin-left: 8px;
                                    border-inline-start: 1px solid;
                                    border-color: ${(props) => props?.theme?.line?.light};
                                    > * {
                                        margin-right: 8px;
                                        &:last-child {
                                            margin-right: 0;
                                        }
                                    }
                                }
                            }
                            .own_names {
                                ${b2xs(props?.theme?.text?.mid, 400)};
                            }
                            .action_wrap {
                                margin-top: 16px;
                                .fill-btn{
                                    @media ${Media.mobile}{
                                        width: 100%;
                                    }
                                }
                            }
                        }
                    }
                    .infomodal_separator {
                        margin: 0 -32px;
                        margin-top: 32px;
                        border: 0;
                        border-bottom: 1px solid ${(props) => props?.theme?.line?.light};
                        @media ${Media.ultra_mobile}{
                            margin: 0 -24px;
                            margin-top: 24px;
                        }
                    }
                    .bottom_desc {
                        margin-top: 32px;
                        @media ${Media.ultra_mobile}{
                            margin-top: 24px;
                        }
                        h3{
                            margin-bottom: 16px;
                        }
                        table {
                            width: 100%;
                            tr{
                                td{
                                    padding: 6px 12px;
                                    &:first-child{
                                        ${b2xs(props?.theme?.text?.high, 500)};
                                        padding-left: 0;
                                    }
                                    &:last-child{
                                        ${b2xs(props?.theme?.text?.high, 400)};
                                        padding-right: 0;
                                    }   
                                }
                                &:first-child{
                                    td{
                                        padding-top: 0;
                                    }
                                }
                                &:last-child{
                                    td{
                                        padding-bottom: 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }   
        `
    }
`
import { Btn } from "@dilpesh/kgk-ui-library";
import { CloseIcon, getSessionItem, SESSION_KEYS, useGetMetaFileInformation } from "@magneto-it-solutions/kgk-common-library";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import { AssetsInfoModalStyle } from "./assets-info-modal.style";

interface AssetsInfoModalProps {
    show: boolean;
    onHide: () => void;
    rejectBtnEvent: () => void;
    selectedFile?: any;
    storagePath?: any;
}

export default function AssetsInfoModal({
    show,
    onHide,
    rejectBtnEvent,
    selectedFile,
    storagePath
}: AssetsInfoModalProps) {

    const { getMetaFileInformation, isLoading, data: fileMetaData, error } = useGetMetaFileInformation();
    const [fileDetails, setFileDetails] = useState<any>(null);

    // Call API when modal opens with a selected file
    useEffect(() => {
        if (show && selectedFile?._id) {
            console.log('Fetching file information for:', selectedFile);
            getMetaFileInformation(selectedFile._id);
        }
    }, [show, selectedFile, getMetaFileInformation]);

    // Update file details when API data is received
    useEffect(() => {
        if (fileMetaData) {
            console.log('File meta data received:', fileMetaData);
            setFileDetails(fileMetaData);
        }
    }, [fileMetaData]);

    // Format file size
    const formatFileSize = (bytes: number) => {
        if (!bytes) return 'Unknown';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    };
    // Build file details data from API response or selected file
    const fileDetailsData = [
        {
            title: 'Type',
            value: selectedFile?.file_name?.split('.').pop()?.toUpperCase() || 'Unknown'
        },
        {
            title: 'Size',
            value: selectedFile?.file_size ? formatFileSize(selectedFile.file_size) : 'Unknown'
        },
        {
            title: 'Location',
            value: ['Assets', ...(storagePath || []).map((item: any) => item.name).reverse()].join(' > ')
        },
        {
            title: 'Owner',
            value: fileDetails?.owner || 'Me'
        },
        {
            title: 'Modified',
            value: selectedFile.updatedAt ? format(new Date(selectedFile.updatedAt), getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a") : '-'
        },
        {
            title: 'Created',
            value: selectedFile.createdAt ? format(new Date(selectedFile.createdAt), getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a") : '-'
        },
    ];

    // Get file name for modal title
    const fileName = selectedFile?.file_name || 'File Information';

    return (
        <AssetsInfoModalStyle
            title={fileName}
            centered
            closeIcon={<CloseIcon />}
            open={show}
            onOk={onHide}
            onCancel={onHide}
            width={464}
            wrapClassName="detail_modal assets_info_modal"
            footer={null}
        >
            <div className="inner_section_wrap">
                <div className="top_section">
                    <div className="img_wpr">

                        <img
                            src={process.env.NEXT_PUBLIC_ASSETS_URL + selectedFile?.path}
                            alt={fileName}
                            onError={(e) => {
                                (e.target as HTMLImageElement).src = "/assets-crm/assets/img/modalinfo_image.png";
                            }}
                        />

                    </div>
                    <div className="access_users">
                        <h3>Who has access</h3>
                        {/* <div className="image_wrap">
                            <div className="owner_img">
                                <AccessUserOwner />
                            </div>
                            <div className="shared_img">
                                <AccessUser2 />
                                <AccessUser3 />
                            </div>
                        </div> */}
                        <p className="own_names">Owned by you. Shared with James White and Alena Smith.</p>
                        <div className="action_wrap">
                            <Btn bg={"fill"} size={"large"}>Manage access</Btn>
                        </div>
                    </div>
                </div>
                <hr className="infomodal_separator" />
                <div className="bottom_desc">
                    <h3>File details</h3>
                    <table>
                        {
                            fileDetailsData?.map((item: any) => (
                                <tr>
                                    <td>{item.title}</td>
                                    <td>{item.value}</td>
                                </tr>
                            ))
                        }
                    </table>
                </div>
            </div>
            <form className="body">

            </form>
        </AssetsInfoModalStyle>
    )
}

import { Btn, CheckboxLabel, InputGroups } from "@dilpesh/kgk-ui-library";
import { CheckIcon, CloseIcon, PermissionDownArrow } from "@magneto-it-solutions/kgk-common-library";
import { Col, Dropdown, MenuProps, Row } from "antd";
import Link from "next/link";
import { useState } from "react";
import { AssetsShareModalStyle } from "./assets-share-modal.style";

export default function AssetsShareModal({
    show,
    onHide,
    rejectBtnEvent,
}: any) {

    const items: MenuProps['items'] = [
        {
            label: 'Editor',
            key: '0',
        },
        {
            label: 'Commenter',
            key: '1',
        },
    ];

    const restrictitems: MenuProps['items'] = [
        {
            label: <><CheckIcon />Restricted</>,
            key: '0',
        },
        {
            label: <><CheckIcon />Anyone with the link</>,
            key: '1',
        },
    ];

    const [anyoneFlag, setanyoneFlag] = useState(false);

    const handleDropdownItemClick = (e: any) => {
        console.log(e.key);
    };

    return (
        <AssetsShareModalStyle
            title={'Share ‘KGK_Logo_Black.JPG'}
            centered
            closeIcon={<CloseIcon />}
            open={show}
            onOk={onHide}
            onCancel={onHide}
            width={464}
            wrapClassName="detail_modal assets_share_modal"
            footer={[
                <Btn onClick={rejectBtnEvent} size="large">
                    Cancel
                </Btn>,
                <Btn bg="fill" size="large">
                    Share
                </Btn>,
            ]}
        >
            <form className="body">
                <Row>
                    <Col xs={24}>
                        <InputGroups
                            label="Enter email address"
                        />
                    </Col>
                </Row>
            </form>
            <div className="peopleaccess_wrap" id="selection_access">
                <h3>People with access</h3>
                <div className="user_wpr">
                    <div className="inner_user_part">
                        <div className="user_info">
                            <div className="img_wrap">
                                <img src="/assets-crm/assets/img/share-modal/share-1.png" />
                            </div>
                            <div className="details_wrap">
                                <div className="dark_title">Albert Flores</div>
                                <p className="light_identity"><EMAIL></p>
                            </div>
                        </div>
                        <div className="shareable_info"><p>Owner</p></div>
                    </div>
                    <div className="inner_user_part">
                        <div className="user_info">
                            <div className="img_wrap">
                                <img src="/assets-crm/assets/img/share-modal/share-2.png" />
                            </div>
                            <div className="details_wrap">
                                <div className="dark_title">Brooklyn Simmons</div>
                                <p className="light_identity"><EMAIL></p>
                            </div>
                        </div>
                        <div className="shareable_info">
                            <div className="optional_select">
                                <Dropdown
                                    trigger={['click']}
                                    placement="bottomRight"
                                    // arrow={{ pointAtCenter: false }}
                                    overlayClassName="viewer_dropdown_menu"
                                    dropdownRender={() => (
                                        <div className="ant-dropdown-menu">
                                            <div className="this_links_wpr">
                                                <div className="check_link_wpr">
                                                    <ul>
                                                        <li className="active"><Link href={'#'}><CheckIcon />Viewer</Link></li>
                                                        <li><Link href={'#'}><CheckIcon />Owner</Link></li>
                                                    </ul>
                                                </div>
                                                <div className="plain_link_wpr">
                                                    <ul>
                                                        <li className="red"><Link href={'#'}>Remove access</Link></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div className="ck_links_wpr">
                                                <ul>
                                                    <li><CheckboxLabel>Brand</CheckboxLabel></li>
                                                    <li><CheckboxLabel>SKU</CheckboxLabel></li>
                                                    <li><CheckboxLabel>Price</CheckboxLabel></li>
                                                    <li><CheckboxLabel>Collection</CheckboxLabel></li>
                                                    <li><CheckboxLabel>Business Category</CheckboxLabel></li>
                                                </ul>
                                            </div>
                                        </div>
                                    )}
                                >
                                    <div className="trigger_wpr">
                                        Viewer
                                        <div className="arrow_wpr">
                                            <PermissionDownArrow />
                                        </div>
                                    </div>
                                </Dropdown>
                            </div>
                        </div>
                    </div>
                    <div className="inner_user_part">
                        <div className="user_info">
                            <div className="img_wrap">
                                <img src="/assets-crm/assets/img/share-modal/share-3.png" />
                            </div>
                            <div className="details_wrap">
                                <div className="dark_title">Ralph Edwards</div>
                                <p className="light_identity"><EMAIL></p>
                            </div>
                        </div>
                        <div className="shareable_info">
                            <div className="optional_select">
                                <Dropdown
                                    trigger={['click']}
                                    placement="bottomRight"
                                    // arrow={{ pointAtCenter: false }}
                                    overlayClassName="viewer_dropdown_menu"
                                    dropdownRender={() => (
                                        <div className="ant-dropdown-menu">
                                            <div className="this_links_wpr">
                                                <div className="check_link_wpr">
                                                    <ul>
                                                        <li className="active"><Link href={'#'}><CheckIcon />Viewer</Link></li>
                                                        <li><Link href={'#'}><CheckIcon />Owner</Link></li>
                                                    </ul>
                                                </div>
                                                <div className="plain_link_wpr">
                                                    <ul>
                                                        <li className="red"><Link href={'#'}>Remove access</Link></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div className="ck_links_wpr">
                                                <ul>
                                                    <li><CheckboxLabel>Brand</CheckboxLabel></li>
                                                    <li><CheckboxLabel>SKU</CheckboxLabel></li>
                                                    <li><CheckboxLabel>Price</CheckboxLabel></li>
                                                    <li><CheckboxLabel>Collection</CheckboxLabel></li>
                                                    <li><CheckboxLabel>Business Category</CheckboxLabel></li>
                                                </ul>
                                            </div>
                                        </div>
                                    )}
                                >
                                    <div className="trigger_wpr">
                                        Viewer
                                        <div className="arrow_wpr">
                                            <PermissionDownArrow />
                                        </div>
                                    </div>
                                </Dropdown>
                            </div>
                        </div>
                    </div>
                    <div className="inner_user_part">
                        <div className="user_info">
                            <div className="img_wrap">
                                <img src="/assets-crm/assets/img/share-modal/share-4.png" />
                            </div>
                            <div className="details_wrap">
                                <div className="dark_title">Floyd Miles</div>
                                <p className="light_identity"><EMAIL></p>
                            </div>
                        </div>
                        <div className="shareable_info">
                            <div className="optional_select">
                                <Dropdown
                                    trigger={['click']}
                                    placement="bottomRight"
                                    // arrow={{ pointAtCenter: false }}
                                    overlayClassName="viewer_dropdown_menu"
                                    dropdownRender={() => (
                                        <div className="ant-dropdown-menu">
                                            <div className="this_links_wpr">
                                                <div className="check_link_wpr">
                                                    <ul>
                                                        <li className="active"><Link href={'#'}><CheckIcon />Viewer</Link></li>
                                                        <li><Link href={'#'}><CheckIcon />Owner</Link></li>
                                                    </ul>
                                                </div>
                                                <div className="plain_link_wpr">
                                                    <ul>
                                                        <li className="red"><Link href={'#'}>Remove access</Link></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div className="ck_links_wpr">
                                                <ul>
                                                    <li><CheckboxLabel>Brand</CheckboxLabel></li>
                                                    <li><CheckboxLabel>SKU</CheckboxLabel></li>
                                                    <li><CheckboxLabel>Price</CheckboxLabel></li>
                                                    <li><CheckboxLabel>Collection</CheckboxLabel></li>
                                                    <li><CheckboxLabel>Business Category</CheckboxLabel></li>
                                                </ul>
                                            </div>
                                        </div>
                                    )}
                                >
                                    <div className="trigger_wpr">
                                        Viewer
                                        <div className="arrow_wpr">
                                            <PermissionDownArrow />
                                        </div>
                                    </div>
                                </Dropdown>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="general_access_wpr">
                    <h3>General access</h3>
                    <div className="inner_user_part" id="general_share">
                        <div className="user_info">
                            <div className="img_wrap">
                                <img src="/assets-crm/assets/img/share-modal/global.png" />
                            </div>
                            <div className="details_wrap">
                                <div className="dark_title">
                                    <Dropdown
                                        menu={{
                                            items: restrictitems, onClick: handleDropdownItemClick, selectable: true,
                                            defaultSelectedKeys: ['0'],
                                        }}
                                        trigger={['click']}
                                        getPopupContainer={() => document.getElementById('general_share') as HTMLElement}
                                    >
                                        <a onClick={(e) => e.preventDefault()}>
                                            Anyone with the link
                                            <div className="arrow_wpr">
                                                <PermissionDownArrow />
                                            </div>
                                        </a>
                                    </Dropdown>
                                </div>
                                <p className="light_identity">Anyone on the internet with the link can view</p>
                            </div>
                        </div>
                        <div className="shareable_info">
                            <div className="optional_select">
                                <Dropdown
                                    trigger={['click']}
                                    placement="bottomRight"
                                    // arrow={{ pointAtCenter: false }}
                                    overlayClassName="viewer_dropdown_menu"
                                    dropdownRender={() => (
                                        <div className="ant-dropdown-menu">
                                            <div className="this_links_wpr">
                                                <div className="check_link_wpr">
                                                    <ul>
                                                        <li className="active"><Link href={'#'}><CheckIcon />Viewer</Link></li>
                                                        <li><Link href={'#'}><CheckIcon />Owner</Link></li>
                                                    </ul>
                                                </div>
                                                <div className="plain_link_wpr">
                                                    <ul>
                                                        <li className="red"><Link href={'#'}>Remove access</Link></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div className="ck_links_wpr">
                                                <ul>
                                                    <li><CheckboxLabel>Brand</CheckboxLabel></li>
                                                    <li><CheckboxLabel>SKU</CheckboxLabel></li>
                                                    <li><CheckboxLabel>Price</CheckboxLabel></li>
                                                    <li><CheckboxLabel>Collection</CheckboxLabel></li>
                                                    <li><CheckboxLabel>Business Category</CheckboxLabel></li>
                                                </ul>
                                            </div>
                                        </div>
                                    )}
                                >
                                    <div className="trigger_wpr">
                                        Viewer
                                        <div className="arrow_wpr">
                                            <PermissionDownArrow />
                                        </div>
                                    </div>
                                </Dropdown>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AssetsShareModalStyle>
    )
}
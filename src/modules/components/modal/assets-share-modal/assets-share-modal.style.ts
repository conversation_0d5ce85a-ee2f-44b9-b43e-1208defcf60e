import { Media, b2xs, b3xs, bs } from "@dilpesh/kgk-ui-library";
import { Modal } from "antd";
import styled, { css } from "styled-components";

export const AssetsShareModalStyle = styled(Modal)`
    ${(props) =>
        css`
            .peopleaccess_wrap {
                margin-top: 24px;
                & > h3 {
                    ${bs(props?.theme?.text?.high, 500)};
                    margin-bottom: 16px;
                }
                .inner_user_part {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    &:not(:last-child) {
                        margin-bottom: 16px;
                    }
                    .user_info {
                        display: flex;
                        align-items: center;
                        .img_wrap {
                            width: 40px;
                            height: 40px;
                            border-radius: 100%;
                            overflow: hidden;
                            @media ${Media.mobile}{
                                width: 32px;
                                height: 32px;
                            }
                        }
                        .details_wrap {
                            padding-left: 16px;
                            width: calc(100% - 40px);
                            @media ${Media.mobile}{
                                width: calc(100% - 32px);
                                padding-left: 12px;
                            }
                            .dark_title {
                                ${b2xs(props?.theme?.text?.high, 500)};
                                .ant-dropdown-trigger {
                                    ${b2xs(props?.theme?.text?.high, 500)};
                                }
                            }
                            .light_identity {
                                ${b3xs(props?.theme?.text?.mid, 400)};
                            }
                        }
                    }
                    .shareable_info {
                        margin-left: 16px;
                        & > p {
                            ${b3xs(props?.theme?.text?.high, 400, false)};
                        }
                        .optional_select {
                            .ant-dropdown-trigger {
                                ${b3xs(props?.theme?.text?.high, 400, false)};
                                display: inline-flex;
                                align-items: center;
                                justify-content: flex-start;
                                cursor: pointer;
                                width: ;
                                .arrow_wpr{
                                    height: 16px;
                                    width: 16px;
                                    margin-left: 4px;
                                }
                            }
                        }
                    }
                }
                .general_access_wpr {
                    margin-top: 24px;
                    & > h3 {
                        ${bs(props?.theme?.text?.high, 500)};
                        margin-bottom: 16px;
                    }
                    .ant-dropdown-trigger {
                        ${b2xs(props?.theme?.text?.high, 500)};
                        display: inline-flex;
                        justify-content: flex-start;
                        align-items: center;
                        .arrow_wpr{
                            height: 16px;
                            width: 16px;
                            margin-left: 8px;
                        }
                    }
                    .ant-dropdown-menu {
                        padding: 8px 0;
                        .ant-dropdown-menu-item {
                            padding: 6px 16px;
                            ${b2xs(props?.theme?.text?.high, 400, false)};
                            position: relative;
                            transition: all 300ms ease-in;
                            margin-bottom: 4px;
                            &:last-child{
                                margin-bottom: 0;
                            }
                            &:hover {
                                background-color: ${(props) => props?.theme?.colors?.brand_low};
                                transition: all 300ms ease-in;
                            }
                            span{
                                display: flex;
                                position: relative;
                                padding-left: 28px;
                                svg{
                                    height: 20px;
                                    width: 20px;
                                    position: absolute;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    left: 0;
                                    visibility: hidden;
                                    transition: all 200ms ease-in;
                                    path{
                                        stroke: ${(props.theme.colors.brand_high)};
                                    }
                                }
                            }
                            &.ant-dropdown-menu-item-selected {
                                background-color: ${(props) => props?.theme?.colors?.brand_low};
                                svg{
                                    visibility: visible;
                                    transition: all 200ms ease-in;
                                }
                            }
                        }
                    }
                }
            }
        `
    }
`
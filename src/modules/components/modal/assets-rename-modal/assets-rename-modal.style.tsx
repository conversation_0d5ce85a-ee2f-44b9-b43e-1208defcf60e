import { Modal } from "antd";
import styled from "styled-components";

export const AssetsRenameModalStyle = styled(Modal)`
    .ant-modal-content {
        border-radius: 16px;
        overflow: hidden;
        
        .ant-modal-header {
            padding: 24px 24px 0;
            border: none;
            
            .ant-modal-title {
                font-size: 20px;
                font-weight: 600;
                color: #1a1a1a;
            }
        }
        
        .ant-modal-body {
            padding: 24px;
            
            .body {
                .ant-row {
                    margin-bottom: 0;
                }
            }
        }
        
        .ant-modal-footer {
            padding: 0 24px 24px;
            border: none;
            text-align: right;
            
            .ant-btn {
                margin-left: 12px;
                
                &:first-child {
                    margin-left: 0;
                }
            }
        }
        
        .ant-modal-close {
            top: 16px;
            right: 16px;
            
            .ant-modal-close-x {
                width: 32px;
                height: 32px;
                line-height: 32px;
                
                svg {
                    width: 16px;
                    height: 16px;
                }
            }
        }
    }
`;

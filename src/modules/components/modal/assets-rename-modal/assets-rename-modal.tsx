import { Btn, InputGroups } from "@dilpesh/kgk-ui-library";
import { CloseIcon, useAssetRename } from "@magneto-it-solutions/kgk-common-library";
import { AssetType } from "@magneto-it-solutions/kgk-common-library/lib/esm/store/assets/types";
import { Col, Row } from "antd";
import { useEffect, useState } from "react";
import { AssetsRenameModalStyle } from "./assets-rename-modal.style";

interface AssetsRenameModalProps {
    show: boolean;
    onHide: () => void;
    rejectBtnEvent: () => void;
    item?: any;
    itemType: AssetType;
    onSuccess?: (newName: string) => void;
}

export default function AssetsRenameModal({
    show,
    onHide,
    rejectBtnEvent,
    item,
    itemType,
    onSuccess,
}: AssetsRenameModalProps) {
    const [newName, setNewName] = useState('');
    const [error, setError] = useState('');
    const { assetRename, isLoading: isRenameLoading } = useAssetRename();
    // Set initial name when modal opens
    useEffect(() => {
        if (show && item) {
            const currentName = itemType === 'folder' ? item.folder_name : item.file_name;
            setNewName(currentName || '');
            setError('');
        }
    }, [show, item, itemType]);

    const handleCancel = () => {
        setNewName('');
        setError('');
        rejectBtnEvent();
    };

    const handleRename = async () => {
        if (!newName.trim()) {
            setError(`${itemType === 'folder' ? 'Folder' : 'File'} name is required`);
            return;
        }

        if (newName.trim() === (itemType === 'folder' ? item?.folder_name : item?.file_name)) {
            setError('Please enter a different name');
            return;
        }

        setError('');

        try {
            assetRename({
                asset_id: item._id,
                asset_name: newName.trim(),
                asset_type: itemType,
            }).then(() => {
                onHide();
                setNewName('');
                setError('');
            })


        } catch (error: any) {
            console.error(`Error renaming ${itemType}:`, error);
            setError(error.message || `Failed to rename ${itemType}`);
        } finally {
            // setIsLoading(false);
        }
    };

    const getTitle = () => {
        return `Rename ${itemType === 'folder' ? 'folder' : 'file'}`;
    };

    const getLabel = () => {
        return `${itemType === 'folder' ? 'Folder' : 'File'} name`;
    };

    const getPlaceholder = () => {
        return `Enter ${itemType === 'folder' ? 'folder' : 'file'} name`;
    };

    return (
        <AssetsRenameModalStyle
            title={getTitle()}
            centered
            closeIcon={<CloseIcon />}
            open={show}
            onOk={onHide}
            onCancel={onHide}
            width={464}
            wrapClassName="detail_modal"
            footer={[
                <Btn key="cancel" onClick={handleCancel} size="large" disabled={isRenameLoading}>
                    Cancel
                </Btn>,
                <Btn
                    key="rename"
                    bg="fill"
                    size="large"
                    onClick={handleRename}
                    isloading={isRenameLoading}
                    disabled={!newName.trim() || isRenameLoading}
                >
                    Rename
                </Btn>,
            ]}
        >
            <form className="body" onSubmit={(e) => { e.preventDefault(); handleRename(); }}>
                <Row>
                    <Col xs={24}>
                        <InputGroups
                            label={getLabel()}
                            value={newName}
                            onChange={(e: any) => setNewName(e.target.value)}
                            placeholder={getPlaceholder()}
                            disabled={isRenameLoading}
                            error={error}
                        />
                    </Col>
                </Row>
            </form>
        </AssetsRenameModalStyle>
    )
}

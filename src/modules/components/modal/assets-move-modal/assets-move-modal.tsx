import { FolderAssetsIcon, FolderUploadIcon } from "@/assets/icons";
import { Btn } from "@dilpesh/kgk-ui-library";
import { CloseIcon, RightArrow, useGetFolderDataQuery, useMoveAssets } from "@magneto-it-solutions/kgk-common-library";
import { useEffect, useState } from "react";
import AssetsNewFolderModal from "../assets-new-folder-modal/assets-new-folder-modal";
import { AssetsMoveModalStyle } from "./assets-move-modal.style";

interface AssetsMoveModalProps {
    show: boolean;
    onHide: () => void;
    rejectBtnEvent: () => void;
    selectedItems?: any[];
    currentFolderId?: string;
    onMoveSuccess?: () => void;
}

interface BreadcrumbItem {
    id: string;
    name: string;
}

export default function AssetsMoveModal({
    show,
    onHide,
    rejectBtnEvent,
    selectedItems = [],
    currentFolderId = '',
    onMoveSuccess,
}: AssetsMoveModalProps) {
    const [modalFolderId, setModalFolderId] = useState('');
    const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([]);
    const [selectedDestinationFolder, setSelectedDestinationFolder] = useState<string | null>(null);
    const [showNewFolderModal, setShowNewFolderModal] = useState(false);
    const { data: folderData, isLoading, isError, refetch } = useGetFolderDataQuery({
        id: modalFolderId,
        sort: 'folder_name',
        order: 'asc',
    });
    
    const { moveAssets, isLoading: isMoving } = useMoveAssets();

    // Build breadcrumbs from API response
    useEffect(() => {
        if (!modalFolderId) {
            // Root level - show default breadcrumbs
            setBreadcrumbs([]);
        } else if (folderData?.parents) {
            const reversedParents = folderData?.parents.slice().reverse(); // Oldest parent first
            setBreadcrumbs(reversedParents.map((parent: any) => ({
                id: parent.id,
                name: parent.name
            })));
        }
    }, [modalFolderId, folderData]);

    // Handle folder navigation
    const handleFolderNavigate = (e: React.MouseEvent, folder: any) => {
        e.preventDefault();
        e.stopPropagation();
        setModalFolderId(folder._id);
        setSelectedDestinationFolder(null); // Clear selection when navigating
    };

    // Handle breadcrumb navigation
    const handleBreadcrumbClick = (e: React.MouseEvent, folderId: string) => {
        e.preventDefault();
        e.stopPropagation();
        setModalFolderId(folderId);
        setSelectedDestinationFolder(null);
    };

    // Handle folder selection for move
    const handleFolderSelect = (e: React.MouseEvent, folder: any) => {
        e.preventDefault();
        e.stopPropagation();
        setSelectedDestinationFolder(folder._id);
    };

    // Handle new folder creation
    const handleNewFolderClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setShowNewFolderModal(true);
    };

    // Handle new folder creation success
    const handleNewFolderSuccess = () => {
        setShowNewFolderModal(false);
        // Refetch the current folder data to show the new folder
        refetch();
    };

    // Check if destination is valid (not moving folder into itself or its children)
    const isValidDestination = (destinationId: string) => {
        // Can't move into the same folder
        if (selectedItemIds.includes(destinationId)) {
            return false;
        }

        // For folders, check if destination is a child of any selected folder
        // This would require checking the folder hierarchy, but for now we'll allow it
        // TODO: Add proper hierarchy check to prevent moving folder into its children

        return true;
    };

    // Handle move action
    const handleMove = () => {
        // Use selected destination folder if available, otherwise use current folder
        const destinationFolderId = selectedDestinationFolder || modalFolderId;

        if (destinationFolderId && !isValidDestination(destinationFolderId)) {
            console.log('Invalid destination: Cannot move folder into itself');
            return;
        }

        moveAssets({
            asset_id: selectedItems[0]?._id,
            parent_folder_id: destinationFolderId || null,
            asset_type: selectedItems[0]?.file_name ? 'file' : 'folder',
        }).then(() => {
            onMoveSuccess?.();
            onHide();
        })
    };

    // Get folders from API data and filter out selected items
    const allFolders = folderData?.folders || [];

    // Get IDs of selected items to exclude from destination list
    const selectedItemIds = selectedItems.map(item => item._id);

    // Filter out selected folders from destination list (can't move folder into itself)
    const folders = allFolders.filter(folder => !selectedItemIds.includes(folder._id));

    return (
        <>
            <AssetsMoveModalStyle
                title={
                    <div className="custom_header">
                        <h2>Move {selectedItems.length} item{selectedItems.length !== 1 ? 's' : ''}</h2>
                        <div className="current_location">
                            <p>Current location:</p>
                            <div className="folder_icon">
                                <FolderAssetsIcon />
                                <p>
                                    <span
                                        className={`breadcrumb-root ${modalFolderId ? 'clickable' : 'default'}`}
                                        onClick={(e) => modalFolderId && handleBreadcrumbClick(e, '')}
                                    >
                                        Assets
                                    </span>
                                    {breadcrumbs.map((crumb, index) => (
                                        <span key={crumb.id}>
                                            {' > '}
                                            <span
                                                className={`breadcrumb-item ${index !== breadcrumbs.length - 1 ? 'clickable' : 'default'}`}
                                                onClick={(e) => {
                                                    if (index !== breadcrumbs.length - 1) {
                                                        handleBreadcrumbClick(e, crumb.id);
                                                    }
                                                }}
                                            >
                                                {crumb.name}
                                            </span>
                                        </span>
                                    ))}

                                </p>
                            </div>
                        </div>
                    </div>
                }
                centered
                closeIcon={<CloseIcon />}
                open={show}
                onOk={onHide}
                onCancel={onHide}
                width={464}
                wrapClassName="assets_move_modal"
                footer={
                    <div className="custom_footer">
                        <div className="new_folderwpr">
                            <button
                                type="button"
                                onClick={handleNewFolderClick}
                                title="Create new folder"
                            >
                                <FolderUploadIcon />
                            </button>
                        </div>
                        <div className="buttons_wrap">
                            <Btn onClick={rejectBtnEvent} size="large">
                                Cancel
                            </Btn>
                            <Btn
                                bg="fill"
                                size="large"
                                onClick={handleMove}
                            >
                                Move
                            </Btn>
                        </div>
                    </div>
                }
            >
                <div className="custom_modal_body">
                    {isLoading ? (
                        <div className="loading-state">
                            Loading folders...
                        </div>
                    ) : isError ? (
                        <div className="error-state">
                            Error loading folders
                        </div>
                    ) : folders.length === 0 ? (
                        <div className="empty-state">
                            No subfolders available. Items will be moved to the current folder.
                        </div>
                    ) : (
                        <>
                            {/* <div className="folder-list-header">
                                Select a destination folder or click Move to use current folder:
                            </div> */}
                            {folders.map((folder: any) => (
                                <div
                                    key={folder._id}
                                    className={`item_wrap ${selectedDestinationFolder === folder._id ? 'selected' : ''}`}
                                >
                                    <div
                                        className="dynamic_part clickable"
                                        onClick={(e) => handleFolderNavigate(e, folder)}
                                        title="Click to navigate into this folder"
                                    >
                                        <FolderAssetsIcon />
                                        <p className="item_name">{folder.folder_name}</p>
                                    </div>
                                    <button
                                        type="button"
                                        className="move_section"
                                        onClick={(e) => handleFolderSelect(e, folder)}
                                    >
                                        <p>Select</p>
                                        <RightArrow />
                                    </button>
                                </div>
                            ))}
                        </>
                    )}
                </div>
            </AssetsMoveModalStyle >

            {/* New Folder Modal */}
            {showNewFolderModal && (
                <AssetsNewFolderModal
                    show={showNewFolderModal}
                    onHide={() => setShowNewFolderModal(false)}
                    rejectBtnEvent={() => setShowNewFolderModal(false)}
                    currentFolderId={modalFolderId}
                    onSuccess={handleNewFolderSuccess}
                />
            )}
        </>
    )
}
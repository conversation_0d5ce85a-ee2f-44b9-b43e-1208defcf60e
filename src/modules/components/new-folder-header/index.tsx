import { FileUploadIcon, FolderUploadIcon, NewFolder, WhitePlus } from "@/assets/icons";
import { Btn, CustomeDropdown } from "@dilpesh/kgk-ui-library";
import { useRef } from "react";
import { itemsForList } from "../../utils/constant";

interface NewFolderHeaderProps {
    onNewFolderClick?: () => void;
    onFileUpload?: (files: File[], parentFolderId?: string) => void;
    currentFolderId?: string;
}

const NewFolderHeader: React.FC<NewFolderHeaderProps> = ({
    onNewFolderClick,
    onFileUpload,
    currentFolderId
}) => {
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileUploadClick = () => {
        fileInputRef.current?.click();
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (files && files.length > 0 && onFileUpload) {
            const fileArray = Array.from(files);
            onFileUpload(fileArray, currentFolderId || '');
        }
        // Reset the input value to allow selecting the same file again
        if (event.target) {
            event.target.value = '';
        }
    };

    const newFileItems = [
        {
            key: 1,
            value: <>
                <NewFolder />
                <span>New folder</span>
            </>,
            onClick: onNewFolderClick,
            class: 'icon_text_24_bs'
        },
        {
            type: 'divider',
        },
        {
            key: 2,
            value: <>
                <FileUploadIcon />
                <span>File upload</span>
            </>,
            onClick: handleFileUploadClick,
            class: 'icon_text_24_bs'
        },
        {
            key: 3,
            value: <>
                <FolderUploadIcon />
                <span>Folder upload</span>
            </>,
            class: 'icon_text_24_bs'
        },
    ];

    return (
        <>
            <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                style={{ display: 'none' }}
                multiple
                accept="*/*"
            />
            <CustomeDropdown
                items={itemsForList}
                actionList={newFileItems}
                overlayClassName="icon_dropdown"
            >
                <Btn bg="fill" size="large"><WhitePlus />New</Btn>
            </CustomeDropdown>
        </>
    )
};

export default NewFolderHeader;
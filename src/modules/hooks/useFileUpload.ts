import { useState, useCallback } from 'react';

export interface ICreateFile {
    parent_folder_id: string;
    files: File[];
}

export interface UploadingFile {
    file: File;
    progress: number;
    status: 'uploading' | 'completed' | 'error';
    id: string;
    error?: string;
}

interface UploadCallbacks {
    onProgress?: (fileId: string, progress: number) => void;
    onFileStart?: (fileId: string, fileName: string) => void;
    onFileComplete?: (fileId: string, result: any) => void;
    onFileError?: (fileId: string, error: any) => void;
    onAllComplete?: (results: any[]) => void;
}

export const useFileUpload = () => {
    const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadError, setUploadError] = useState<string | null>(null);

    // Add a new file to the upload queue
    const addUploadingFile = useCallback((fileId: string, file: File) => {
        const newFile: UploadingFile = {
            id: fileId,
            file,
            progress: 0,
            status: 'uploading'
        };
        setUploadingFiles(prev => [...prev, newFile]);
    }, []);

    // Update file progress
    const updateFileProgress = useCallback((fileId: string, progress: number) => {
        setUploadingFiles(prev => 
            prev.map(file => 
                file.id === fileId 
                    ? { ...file, progress: Math.min(progress, 100) }
                    : file
            )
        );
    }, []);

    // Mark file as completed
    const markFileComplete = useCallback((fileId: string) => {
        setUploadingFiles(prev => 
            prev.map(file => 
                file.id === fileId 
                    ? { ...file, progress: 100, status: 'completed' as const }
                    : file
            )
        );
    }, []);

    // Mark file as error
    const markFileError = useCallback((fileId: string, error?: string) => {
        setUploadingFiles(prev => 
            prev.map(file => 
                file.id === fileId 
                    ? { ...file, status: 'error' as const, error }
                    : file
            )
        );
    }, []);

    // Remove a file from upload queue (cancel upload)
    const removeUploadingFile = useCallback((fileId: string) => {
        setUploadingFiles(prev => prev.filter(file => file.id !== fileId));
    }, []);

    // Clear all completed uploads
    const clearCompletedUploads = useCallback(() => {
        setUploadingFiles(prev => prev.filter(file => file.status === 'uploading'));
    }, []);

    // Clear all uploads
    const clearAllUploads = useCallback(() => {
        setUploadingFiles([]);
        setUploadError(null);
    }, []);

    // Simulate file upload API call
    const uploadFile = useCallback(async (
        file: File, 
        fileId: string, 
        parentFolderId: string,
        callbacks: UploadCallbacks
    ) => {
        try {
            callbacks.onFileStart?.(fileId, file.name);
            
            // Simulate upload progress
            for (let progress = 0; progress <= 100; progress += 10) {
                // Check if file was cancelled
                const currentFile = uploadingFiles.find(f => f.id === fileId);
                if (!currentFile) {
                    throw new Error('Upload cancelled');
                }
                
                await new Promise(resolve => setTimeout(resolve, 150));
                callbacks.onProgress?.(fileId, progress);
                
                // Simulate potential error (5% chance)
                if (progress === 50 && Math.random() < 0.05) {
                    throw new Error(`Network error uploading ${file.name}`);
                }
            }
            
            // File upload completed
            const result = { 
                fileId, 
                fileName: file.name, 
                size: file.size,
                parentFolderId,
                uploadedAt: new Date().toISOString()
            };
            
            callbacks.onFileComplete?.(fileId, result);
            return result;
            
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Upload failed';
            callbacks.onFileError?.(fileId, errorMessage);
            throw error;
        }
    }, [uploadingFiles]);

    // Main upload function
    const uploadFiles = useCallback(async (payload: ICreateFile) => {
        setIsUploading(true);
        setUploadError(null);
        
        const results: any[] = [];
        const callbacks: UploadCallbacks = {
            onFileStart: (fileId: string, fileName: string) => {
                const file = payload.files.find(f => f.name === fileName);
                if (file) {
                    addUploadingFile(fileId, file);
                }
            },
            onProgress: updateFileProgress,
            onFileComplete: (fileId: string, result: any) => {
                markFileComplete(fileId);
                results.push(result);
            },
            onFileError: (fileId: string, error: any) => {
                markFileError(fileId, error);
            }
        };

        try {
            // Process files sequentially for better progress tracking
            for (let i = 0; i < payload.files.length; i++) {
                const file = payload.files[i];
                const fileId = `${file.name}-${Date.now()}-${i}`;
                
                try {
                    const result = await uploadFile(file, fileId, payload.parent_folder_id, callbacks);
                    results.push(result);
                } catch (error) {
                    console.error(`Failed to upload ${file.name}:`, error);
                    // Continue with next file even if one fails
                }
            }
            
            callbacks.onAllComplete?.(results);
            return { success: true, results };
            
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Upload process failed';
            setUploadError(errorMessage);
            throw error;
        } finally {
            setIsUploading(false);
        }
    }, [addUploadingFile, updateFileProgress, markFileComplete, markFileError, uploadFile]);

    // Get upload statistics
    const getUploadStats = useCallback(() => {
        const total = uploadingFiles.length;
        const completed = uploadingFiles.filter(f => f.status === 'completed').length;
        const failed = uploadingFiles.filter(f => f.status === 'error').length;
        const uploading = uploadingFiles.filter(f => f.status === 'uploading').length;
        
        return { total, completed, failed, uploading };
    }, [uploadingFiles]);

    return {
        // State
        uploadingFiles,
        isUploading,
        uploadError,
        
        // Actions
        uploadFiles,
        removeUploadingFile,
        clearCompletedUploads,
        clearAllUploads,
        
        // Utils
        getUploadStats
    };
};

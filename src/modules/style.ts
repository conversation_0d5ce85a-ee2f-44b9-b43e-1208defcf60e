import { Media, bes, bl, h3 } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const AssetsPageStyle = styled.div`
    ${(props) =>
        css`
            padding-top: 32px;
            padding-bottom: 80px;
            @media ${Media.ultra_mobile}{
                padding-top: 24px;
                /* padding-bottom: 40px; */
            }
            @media ${Media.mobile}{
                padding-top: 16px;
                /* padding-bottom: 32px; */
            }
            .breadcrumb_wrap {
                margin-bottom: 24px;
                @media ${Media.mobile}{
                    margin-bottom: 16px;
                }
            }
            .title_wrap {
                margin-bottom: 40px;
                @media ${Media.ultra_mobile}{
                    margin-bottom: 32px;
                }
                @media ${Media.mobile}{
                    margin-bottom: 24px;
                }
                & > h1 {
                    ${h3(props?.theme?.text?.high, 400)};
                }
            }
            .tablet_filter_btn_wpr{
                @media ${Media.ultra_mobile_above}{
                    display: none;
                }
                margin-bottom: 24px;
            }
            .asset_content_wrap {
                .subheader_wrap {
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                    margin-bottom: 48px;
                    @media ${Media.ultra_mobile}{
                        margin-bottom: 40px;
                    }
                    @media ${Media.mobile}{
                        margin-bottom: 24px;
                    }
                    .custome_dropdown{
                        order: 1;
                        @media ${Media.mobile}{
                            order: 1;
                            flex: 1;
                            margin-right: 24px;
                        }
                        .ant-dropdown-trigger{
                            @media ${Media.mobile}{
                                width: 100%;
                            }
                            .fill-btn{
                                width: 200px;
                                @media ${Media.mobile}{
                                    width: 100%;
                                }
                            }
                        }
                    }
                    .selection_panel {
                        flex: 1;
                        background-color: ${(props) => props?.theme?.colors?.brand_low};
                        margin-right: 64px;
                        display: flex;
                        align-items: center;
                        justify-content: start;
                        padding: 12px;
                        border-radius: 4px;
                        order: 1;
                        @media ${Media.tablet}{
                            margin-right: 32px;
                        }
                        @media ${Media.mobile}{
                            margin-top: 16px;
                            margin-right: 0;
                            order: 3;
                            width: 100%;
                            flex: unset;
                        }
                        svg{
                            path{
                                stroke: ${(props.theme.colors.brand_high)};
                            }
                        }
                        .item_selected {
                            display: flex;
                            align-items: center;
                            justify-content: start;
                            & > p {
                                ${bes(props?.theme?.text?.high, 500)};
                                margin-left: 16px;
                                white-space: nowrap;
                            }
                        }
                        .other_actions {
                            margin-left: 40px;
                            display: flex;
                            align-items: center;
                            justify-content: start;
                            @media ${Media.desktop}{
                                display: none;
                            }
                            & > *{
                                height: 24px;
                                width: 24px;
                                display: block;
                                margin-right: 24px;
                                &:last-child{
                                    margin-right: 0;
                                }
                            }
                        }
                        & > .action_dropdown{
                            margin-left: auto;
                            @media ${Media.desktop_above}{
                                display: none;
                            }
                        }
                    }
                    .custom_managewrap {
                        margin-left: auto;
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;
                        order: 2;
                        /* & > *{
                            margin-right: 24px;
                            @media ${Media.mobile}{
                                margin-right: 16px;
                            }
                            &:last-child{
                                margin-right: 0;
                            }
                        } */
                        .select_wprs {
                            @media ${Media.ultra_mobile}{
                                display: none;
                            }
                            margin-right: 24px;
                            @media ${Media.mobile}{
                                margin-right: 16px;
                            }
                            .sort_by_box {
                                display: flex;
                                justify-content: flex-start;
                                align-items: center;
                                p{
                                    ${bes(props?.theme?.text?.high, 400, false)};
                                    margin-right: 12px;
                                    white-space: nowrap;
                                }
                                .select_groups{
                                    min-width: 200px;
                                }
                            }
                        }
                        .square_btn_group{
                            margin-right: 16px;
                        }
                        .setting_wrap {
                            .border-btn {
                                height: 48px;
                                width: 48px;
                                & > svg {
                                    height: 32px;
                                    width: 32px;
                                }
                            }
                        }
                    }
                }
                & > .sort_by_box{
                    @media ${Media.ultra_mobile_above}{
                        display: none;
                    }
                }
                .datacontent_wrap {
                    &.grid {
                        .inner_section {
                            & > h2 {
                                ${bl(props?.theme?.text?.high, 500)};
                                margin-bottom: 24px;
                                @media ${Media.mobile}{
                                    margin-bottom: 16px;
                                }
                            }
                        }
                        .folder_wrap {
                            margin-bottom: 48px;
                            @media ${Media.ultra_mobile}{
                                margin-bottom: 40px;
                            }
                            @media ${Media.mobile}{
                                margin-bottom: 32px;
                            }
                            .folder_list {
                                margin-bottom: -24px;
                                @media ${Media.mobile}{
                                    margin-bottom: -12px;
                                }
                                & > *{
                                    margin-bottom: 24px;
                                    @media ${Media.mobile}{
                                        margin-bottom: 12px;
                                    }
                                }
                                .icon24_bes_box {
                                    display: flex;
                                    justify-content: flex-start;
                                    align-items: center;
                                    padding-top: 16px;
                                    padding-bottom: 16px;
                                    padding-left: 16px;
                                    padding-right: 12px;
                                    background-color: ${(props) => props?.theme?.colors?.brand_low};
                                    border-radius: 4px;
                                    transition: all 300ms ease-in;
                                    @media ${Media.mobile}{
                                        padding-top: 12px;
                                        padding-bottom: 12px;
                                        padding-left: 12px;
                                        padding-right: 8px;
                                    }
                                    .inner_box {
                                        width: calc(100% - 24px);
                                        display: flex;
                                        justify-content: flex-start;
                                        align-items: center;
                                        & > p {
                                            padding-left: 12px;
                                            ${bes(props?.theme?.text?.high, 500)};
                                            white-space: nowrap;
                                            text-overflow: ellipsis;
                                            overflow: hidden;
                                            width: calc(100% - 24px);
                                            @media ${Media.mobile}{
                                                padding-left: 8px;
                                            }
                                        }
                                        & > svg {
                                            height: 24px;
                                            width: 24px;
                                            path{
                                                stroke: ${(props.theme.colors.brand_high)};
                                            }
                                        }
                                    }
                                    &:hover {
                                        transition: all 300ms ease-in;
                                        background-color: ${(props) => props?.theme?.colors?.brand_mid};
                                    }
                                }
                            }
                        }
                        .files_wrap {
                            & > h2 {
                                ${bl(props?.theme?.text?.high, 500)};
                                margin-bottom: 24px;
                                @media ${Media.mobile}{
                                    margin-bottom: 16px;
                                }
                            }
                            .files_list {
                                margin-bottom: -24px;
                                @media ${Media.mobile}{
                                    margin-bottom: -12px;
                                }
                                & > *{
                                    margin-bottom: 24px;
                                    @media ${Media.mobile}{
                                        margin-bottom: 12px;
                                    }
                                }
                                .withmedia_file {
                                    padding: 8px 8px 8px 8px;
                                    background-color: ${(props) => props?.theme?.colors?.brand_low};
                                    transition: all 300ms ease-in;
                                    border-radius: 4px;
                                    .item_detail {
                                        display: flex;
                                        justify-content: flex-start;
                                        align-items: center;
                                        padding-top: 8px;
                                        padding-left: 8px;
                                        padding-right: 4px;
                                        padding-bottom: 8px;
                                        @media ${Media.mobile}{
                                            padding-top: 4px;
                                            padding-left: 4px;
                                            padding-right: 0;
                                            padding-bottom: 4px;
                                        }
                                        .inner_box {
                                            width: calc(100% - 24px);
                                            display: flex;
                                            justify-content: flex-start;
                                            align-items: center;
                                            position: relative;
                                            & > p {
                                                width: calc(100% - 4px);
                                                padding-left: 32px;
                                                ${bes(props?.theme?.text?.high, 500)};
                                                white-space: nowrap;
                                                text-overflow: ellipsis;
                                                overflow: hidden;
                                            }
                                            & > svg {
                                                position: absolute;
                                                top: 50%;
                                                transform: translateY(-50%);
                                                left: 0;
                                                height: 24px;
                                                width: 24px;
                                                opacity: 1;
                                                transition: opacity 200ms ease-in;
                                                path{
                                                    stroke: ${(props.theme.colors.brand_high)};
                                                }
                                            }
                                            & > .only-ck{
                                                position: absolute;
                                                top: 50%;
                                                transform: translateY(-50%);
                                                left: 0;
                                                opacity: 0;
                                                transition: opacity 200ms ease-in;
                                            }
                                        }
                                    }
                                    .item_image {
                                        margin-top: 8px;
                                        img {
                                            border-radius: 4px;
                                        }
                                    }
                                    &.selected {
                                        transition: all 300ms ease-in;
                                        background-color: ${(props) => props?.theme?.colors?.brand_mid};
                                        .item_detail{
                                            .inner_box{
                                                & > svg {
                                                    opacity: 0;
                                                    transition: opacity 200ms ease-in;
                                                }
                                                & > .only-ck{
                                                    opacity: 1;
                                                    transition: opacity 200ms ease-in;
                                                }
                                            }
                                        }
                                    }
                                    &:hover {
                                        .item_detail{
                                            .inner_box{
                                                & > svg {
                                                    opacity: 0;
                                                    transition: opacity 200ms ease-in;
                                                }
                                                & > .only-ck{
                                                    opacity: 1;
                                                    transition: opacity 200ms ease-in;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    &.listing {
                        .with_radius {
                            svg, img {
                                border-radius: 50%;
                            }
                        }
                        .icon_name_24_8_bes, .icon_name_24_12_bes{
                            display: flex;
                            align-items: center;
                            svg, img{
                                height: 24px;
                                width: 24px;
                                path{
                                    stroke: ${(props.theme.colors.brand_high)};
                                }
                            }
                            p{
                                width: calc(100% - 24px);
                                ${bes(props?.theme?.text?.high, 400)};
                            }
                        }

                        .icon_name_24_12_bes{
                            p{
                                ${bes(props?.theme?.text?.high, 500)};
                                padding-left: 12px;
                            }
                        }
                        .icon_name_24_8_bes{
                            p{
                                padding-left: 8px;
                            }
                        }
                        .action_wpr{
                            margin-block-start: auto;
                            display: inline-flex;
                            & > *{
                                margin-right: 24px;
                                &:last-child{
                                    margin-right: 0;
                                }
                            }
                            & > a{
                                display: block;
                                height: 24px;
                                width: 24px;
                                cursor: pointer;
                                & > svg{
                                    height: inherit;
                                    width: inherit;
                                    path{
                                        stroke: ${(props.theme.colors.brand_high)};
                                    }
                                }
                            }
                        }
                        .action {
                            .action_wpr {
                                opacity: 0;
                                transition: all 300ms ease-in;
                            }
                        }
                        tr {
                            .action.center {
                                line-height: 0;
                                /* border-inline-start: 0; */
                            }
                            &:hover {
                                .action {
                                    .action_wpr {
                                        opacity: 1;
                                        transition: all 300ms ease-in;
                                    }
                                }       
                            }
                            &:last-child {
                                td {
                                        border-bottom: 1px solid ${(props) => props?.theme?.line?.light};
                                }
                            }
                        }
                    }
                }
                .data_footer_info{
                    margin-top: 24px;
                }
            }
            .findLeft{
                height: 1px;
                width: 1px;
                background-color: transparent;
            }
            .uploading_wpr {
                position: fixed;
                bottom: 0;
                width: 368px;
                border: 1px solid ${(props) => props?.theme?.line?.light};
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                z-index: 87;
                overflow: hidden;
                @media ${Media.ultra_mobile}{
                    right: 18px !important;
                    left: 18px !important;
                    width: calc(100% - 36px);
                }
                .heading_wpr {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    background-color: ${(props) => props?.theme?.colors?.brand_low};
                    padding: 16px;
                    & > p {
                        ${bes(props?.theme?.text?.high, 500)};
                    }
                    .action_wpr {
                        display: flex;
                        align-items: center;
                        justify-content: flex-start;
                        .arrow{
                            transform: rotate(0);
                            cursor: pointer;
                            transition: all 200ms ease-in;
                        }
                        & > svg {
                            width: 24px;
                            height: 24px;
                            cursor: pointer;
                            path{
                                stroke: ${(props.theme.colors.brand_high)};
                            }
                        }
                        & > * {
                            margin-right: 12px;
                            &:last-child {
                                margin-right: 0;
                            }
                        }
                    }
                    &.active{
                        .action_wpr{
                            .arrow{
                                transform: rotate(-180deg);
                                transition: all 200ms ease-in;
                            }
                        }
                    }
                }
                .innerdata_wpr {
                    background-color: ${(props.theme.colors.brand_dark_high)};
                    height: 0;
                    overflow: hidden;
                    transition: height 200ms ease-in;
                    &.active {
                        height: auto;
                        overflow: visible;
                        transition: height 200ms ease-in;
                    }
                    .item_box {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 16px;
                        .text_wrap {
                            display: flex;
                            align-items: center;
                            justify-content: flex-start;
                            width: calc(100% - 32px);
                            & > svg {
                                width: 24px;
                                height: 24px;
                                path{
                                    stroke: ${(props.theme.colors.brand_high)};
                                }
                            }
                            & > p {
                                ${bes(props?.theme?.text?.high, 500)};
                                margin-left: 12px;
                            }
                        }
                    }
                }
            }
        `
    }
`
"use client"
import { ActionIcon } from "@/assets/icons";
import { ActionDropdown } from "@dilpesh/kgk-ui-library";


export const getColoumnAssetListing = ({ onFolderClick, onFileClick, onFileInformation, fileActionHandlers, folderActionHandlers }: any) => {
    return {
        name: {
            renderChildren: (item: any) => {
                const handleClick = () => {
                    if (item.type === 'folder' && onFolderClick) {
                        onFolderClick(item.originalData);
                    } else if (item.type === 'file' && onFileClick) {
                        onFileClick(item.originalData);
                    }
                };

                return (
                    <div
                        className='icon_name_24_12_bes'
                        onClick={handleClick}
                        style={{ cursor: 'pointer' }}
                    >
                        {item.nameIcon}
                        <p>{item.name}</p>
                    </div>
                );
            },
        },
        owner: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_8_bes with_radius'>
                        <img src={item.ownerIcon} alt="" />
                        <p>{item.owner}</p>
                    </div>
                );
            },
        },
        lastModifiedBy: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_8_bes with_radius'>
                        <img src={item.lastModifiedByIcon} alt="" />
                        <p>{item.lastModifiedBy}</p>
                    </div>
                );
            },
        },
        action: {
            renderChildren: (item: any) => {
                // Import the action lists from constants
                const { superActionListFile, superActionListFolderWithShare } = require('../utils/constant');
                const { createFileActionHandlers, createFileActionList, createFolderActionHandlers, createFolderActionList } = require('./actionHandlers');

                // Create action list with click handlers based on item type
                let actionitemList;

                console.log('item.type:', item.type);
                console.log('fileActionHandlers:', fileActionHandlers);
                console.log('folderActionHandlers:', folderActionHandlers);

                if (item.type === 'file') {
                    if (fileActionHandlers) {
                        // Use action handlers for files
                        actionitemList = createFileActionList(item.originalData, createFileActionHandlers(fileActionHandlers));
                    } else {
                        // Fallback to original file implementation
                        actionitemList = superActionListFile;
                    }
                } else if (item.type === 'folder') {
                    if (folderActionHandlers) {
                        // Use action handlers for folders
                        actionitemList = createFolderActionList(item.originalData, createFolderActionHandlers(folderActionHandlers), true);
                    } else {
                        // Fallback to original folder implementation
                        actionitemList = superActionListFolderWithShare;
                    }
                } else {
                    // Default fallback
                    actionitemList = superActionListFile;
                }


                // Default fallback for unknown types
                return (
                    <div className='action_wpr'>
                        <ActionDropdown
                            items={{
                                dropId: 'super-action'
                            }}
                            actionList={actionitemList}
                            actionIcon={<ActionIcon />}
                        />
                    </div>
                );

            },
        },
    };
}

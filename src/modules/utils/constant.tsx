import { DeleteIcon, DownloadIcon, FileUploadIcon, FolderInfo, FolderUploadIcon, GridIcon, ListIcon, MoveIcon, NewFolder, ShareIcon } from "@/assets/icons";
import { CopyIcon, EditIcon, LinkIcon } from "@magneto-it-solutions/kgk-common-library";

export const pageStore = [
    {
        link: 'Home',
        title: 'Home'
    },
    {
        title: 'Assets'
    }
]




export const assetsListingHead = [
    {
        label: 'Name',
        key: 'name'
    },
    {
        label: 'Owner',
        key: 'owner'
    },
    {
        label: 'Last modified by',
        key: 'lastModifiedBy'
    },
    {
        label: 'Last modified on',
        key: 'lastModifiedOn'
    },
    {
        label: 'File size',
        key: 'fileSize'
    },
    {
        label: '',
        key: 'action',
        class: 'action center'
    },
]


export const superActionListFile = [
    {
        key: 1,
        value: <>
            <DownloadIcon />
            <span>Download</span>
        </>,
        class: 'icon_text_24_bs'
    },
    {
        key: 2,
        value: <>
            <EditIcon />
            <span>Rename</span>
        </>,
        class: 'icon_text_24_bs'
    },
    {
        key: 3,
        value: <>
            <CopyIcon />
            <span>Duplicate</span>
        </>,
        class: 'icon_text_24_bs'
    },
    {
        key: 4,
        value: <>
            <ShareIcon />
            <span>Share</span>
        </>,
        onClick: () => {
            // onShare
        },
        class: 'icon_text_24_bs'
    },
    {
        key: 5,
        value: <>
            <LinkIcon />
            <span>Copy link</span>
        </>,
        class: 'icon_text_24_bs'
    },
    {
        key: 6,
        value: <>
            <MoveIcon />
            <span>Move</span>
        </>,
        onClick: () => {
            // onMove
        },
        class: 'icon_text_24_bs'
    },
    {
        key: 7,
        value: <>
            <FolderInfo />
            <span>File information</span>
        </>,
        onClick: () => {
            // onInfo
        },
        class: 'icon_text_24_bs'
    },
    // {
    //     type: 'divider',
    // },
    {
        key: 9,
        value: <>
            <DeleteIcon />
            <span>Remove</span>
        </>,
        class: 'icon_text_24_bs'
    },
];

export const newFileItems = [
    {
        key: 1,
        value: <>
            <NewFolder />
            <span>New folder</span>
        </>,
        onClick: () => {
            // onNewFolder
        },
        class: 'icon_text_24_bs'
    },
    {
        type: 'divider',
    },
    {
        key: 2,
        value: <>
            <FileUploadIcon />
            <span>File upload</span>
        </>,
        class: 'icon_text_24_bs'
    },
    {
        key: 3,
        value: <>
            <FolderUploadIcon />
            <span>Folder upload</span>
        </>,
        class: 'icon_text_24_bs'
    },
];

export const sortOptions = [
    {
        label: 'Last modified',
        value: 'Last modified'
    },
    {
        label: 'Best seller',
        value: 'Best seller'
    },
];

export const listViewOptions = [
    {
        value: 'grid',
        content: <GridIcon />
    },
    {
        value: 'listing',
        content: <ListIcon />
    }
];

export const itemsForList = {
    dropId: 'super-action'
};

export const superActionListFolder = [
    {
        key: 1,
        value: <>Open With</>,
        class: 'black'
    },
    {
        key: 2,
        value: <>Rename</>,
        class: 'black'
    },
    {
        key: 3,
        value: <>Remove</>,
        class: 'black'
    }

];

export const superActionListFolderWithShare = [
    {
        key: 2,
        value: <>
            <EditIcon />
            <span>Rename</span>
        </>,
        class: 'icon_text_24_bs'
    },
    {
        key: 3,
        value: <>
            <ShareIcon />
            <span>Share</span>
        </>,
        class: 'icon_text_24_bs'
    },
    {
        key: 4,
        value: <>
            <DeleteIcon />
            <span>Remove</span>
        </>,
        class: 'icon_text_24_bs'
    },
    {
        key: 5,
        value: <>
            <MoveIcon />
            <span>Move</span>
        </>,
        class: 'icon_text_24_bs'
    },
    {
        key: 6,
        value: <>
            <FolderInfo />
            <span>File information</span>
        </>,
        class: 'icon_text_24_bs'
    },
    {
        key: 7,
        value: <>
            <DownloadIcon />
            <span>Download</span>
        </>,
        class: 'icon_text_24_bs'
    }
];

"use client"
import { GlobalStyle } from "@dilpesh/kgk-ui-library";
import { <PERSON>py<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, OfferHeader, TopMsg, useGetAppData } from "@magneto-it-solutions/kgk-common-library";
import { useRouter } from "next/navigation";
import 'react-toastify/dist/ReactToastify.css';
import "swiper/css";
import "swiper/css/navigation";
import FrontendStyle from "../styles/style";
import { useTheme } from "../theme-provider/module-theme-provider";


const CommonLayout = (props: {
  children: React.ReactNode
}) => {
  const theme = useTheme()
  const { isAuthenticated, user } = useGetAppData();
  const router = useRouter();
  return (
    <>
      <GlobalStyle />
      <FrontendStyle />
      <OfferHeader />
      <Header theme={theme ?? "KGK"} router={router} />
      {props.children}
      {(!isAuthenticated || user?.accountType == 'individual') && <Footer />}
      <TopMsg />
      <CopyRight />
    </>
  )
};

export default CommonLayout;
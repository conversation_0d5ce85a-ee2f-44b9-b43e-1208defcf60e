import { LibType } from "@dilpesh/kgk-ui-library";

export const getFaviconUrl = (theme: LibType) => {
  switch (theme) {
    case "Martin":
      return `${process.env.NEXT_PUBLIC_ASSETS_URL}favicons/mf-favicon_1747113418825.webp`;
    case "Entice":
      return `${process.env.NEXT_PUBLIC_ASSETS_URL}favicons/entice-favicon_1747113974052.webp`;
    case "KGK":
      return `${process.env.NEXT_PUBLIC_ASSETS_URL}favicons/kgk-favicon_1747113947598.webp`;
    case "Jewelstone":
      return `${process.env.NEXT_PUBLIC_ASSETS_URL}favicons/kgk-favicon_1747113947598.webp`;
    default:
      return `${process.env.NEXT_PUBLIC_ASSETS_URL}favicons/kgk-favicon_1747113947598.webp`;
  }
};

export async function getThemeFavicon() {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_CMS_BASE_URL}/api/theme`, {
      method: "GET",
      next: { revalidate: 100 },
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
      }
    });

    if (!res.ok) {
      console.error(`Theme API error: ${res.status}`);
      return getFaviconUrl("KGK");
    }

    const data = await res.json();
    const theme = data?.data?.attributes?.theme;
    return getFaviconUrl(theme);
  } catch (error) {
    console.error("Error fetching theme:", error);
    return getFaviconUrl("KGK");
  }
}
import { b2xs, b3xs } from '@dilpesh/kgk-ui-library';
import { createGlobalStyle, css } from 'styled-components';

const FrontendStyle = createGlobalStyle`
    ${(props) =>
        css`
            .viewer_dropdown_menu{
                .ant-dropdown-menu {
                    padding: 8px 0;
                    min-width: 200px;
                    .this_links_wpr{
                        .check_link_wpr{
                            ul{
                                li{
                                    display: block;
                                    margin-bottom: 4px;
                                    &:last-child{
                                        margin-bottom: 0;
                                    }
                                    a{
                                        padding: 6px 16px;
                                        display: flex;
                                        justify-content: flex-start;
                                        align-items: center;
                                        svg{
                                            height: 20px;
                                            width: 20px;
                                            margin-right: 8px;
                                            opacity: 0;
                                            path{
                                                stroke: ${(props.theme.colors.brand_high)};
                                            }
                                        }
                                        ${b2xs(props?.theme?.text?.high, 400, false)};
                                    }
                                    &.active{
                                        a{
                                            svg{
                                                opacity: 1;
                                            }
                                        }
                                    }
                                    &:hover{
                                        background-color: ${(props.theme.colors.brand_low)};
                                    }
                                }
                            }
                        }
                        .plain_link_wpr{
                            ul{
                                li{
                                    display: block;
                                    margin-bottom: 4px;
                                    &:last-child{
                                        margin-bottom: 0;
                                    }
                                    a{
                                        display: block;
                                        padding: 6px 16px;
                                        ${b2xs(props?.theme?.text?.high, 400, false)};
                                    }
                                    &.red{
                                        a{
                                            color: ${(props.theme.status.alert)};
                                        }
                                    }
                                    &:hover{
                                        background-color: ${(props.theme.colors.brand_low)};
                                    }
                                }
                            }
                        }
                    }
                    .ck_links_wpr{
                        margin-top: 12px;
                        border-top: 1px solid ${(props.theme.line.light)};
                        padding-top: 8px;
                        ul{
                            li{
                                margin-bottom: 4px;
                                padding: 4px 16px;
                                .ck{
                                    font-size: 14px;
                                }
                                &:last-child{
                                    margin-bottom: 0;
                                }
                            }
                        }
                    }
                    .ant-dropdown-menu-item {
                        padding: 6px 16px;
                        margin-bottom: 4px;
                        &:last-child {
                            margin-bottom: 0;
                        }
                        .ant-dropdown-menu-title-content {
                            ${b3xs(props?.theme?.text?.high, 400)};
                            display: block;
                        }
                        &:hover {
                            background-color: ${(props) => props.theme.colors.brand_low};
                        }
                    }
                }
            }
            
        `
    } 
`;

export default FrontendStyle;
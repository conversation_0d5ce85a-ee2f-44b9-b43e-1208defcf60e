"use client";

import { IDirectiondir, LibType, UIThemeProvider } from "@dilpesh/kgk-ui-library";
import { useGetAppData } from "@magneto-it-solutions/kgk-common-library";
import { useTheme } from "./module-theme-provider";

const ThemeProvider = (props: {
  children: React.ReactNode;
  theme: LibType;
}) => {
  const { dir } = useGetAppData()
  const theme = useTheme();

  return (
    <>
      <UIThemeProvider libType={theme as LibType} mode={"light"} dir={dir as IDirectiondir}> {props?.children}</UIThemeProvider>
    </>

  )
};

export default ThemeProvider;
import { NextRequest, NextResponse } from "next/server";

async function fetchSupportedLanguages(): Promise<string[]> {
    try {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/v1/language/filter`, {
            method: "POST",
            headers: {
                "x-api-key": process.env.NEXT_PUBLIC_X_API_TOKEN,
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                filters: { dynamicObject: {} },
                pagination: { limit: 50, page: 1 },
                search: "",
                sort: { field: "id", dir: "DESC" },
            }),
        });

        const data = await res.json();
        return data?.data?.data?.map((item: { code: string }) => item.code) || [];
    } catch (error) {
        console.error("Error fetching supported languages:", error);
        return ["en"]; // Return at least default language on error
    }
}

export async function middleware(request: NextRequest) {
    const url = request.nextUrl.clone();
    const pathSegments = url.pathname.split('/').filter(segment => segment);

    // Get first path segment (potential language code)
    const firstSegment = pathSegments[0];

    let response = NextResponse.next();

    // Get supported languages from cookie or fetch them
    let supportedLang = request.cookies.get("supported_language")?.value?.split(",").map(lang => lang.trim());

    if (!supportedLang || supportedLang.length === 0) {
        supportedLang = await fetchSupportedLanguages();
        response.cookies.set("supported_language", supportedLang?.join(","));
    }

    // If first segment is a supported language, we're good - just continue
    if (supportedLang.includes(firstSegment)) {
        response.cookies.set("language", firstSegment);
        return response;
    }

    // At this point, the URL does not have a valid language code, so we need to redirect
    const lang = request.cookies.get("language")?.value || "en";

    // Construct the new URL properly
    if (pathSegments.length === 0) {
        // If we're at the root path
        url.pathname = `/${lang}`;
    } else {
        // We need to insert the language as the first segment
        url.pathname = `/${lang}/${pathSegments.join('/')}`;
    }

    response = NextResponse.redirect(url);
    response.cookies.set("supported_language", supportedLang?.join(","));
    response.cookies.set("language", lang);

    return response;
}

export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - api (API routes)
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * - scene.vjson (3D scene file)
         * - config.json (configuration file)
         * - *.png (PNG image files)
         */
        '/((?!api|_next/static|_next/image|favicon.ico|scene.vjson|config.json|.*\\.png).*)',
        "/"
    ],
};

{"cSpell.words": ["firstname", "<PERSON><PERSON><PERSON>", "lastname", "uuidv"], "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.detectIndentation": false, "editor.snippetSuggestions": "top", "editor.tabSize": 4, "editor.autoIndent": "full", "editor.acceptSuggestionOnCommitCharacter": true, "editor.autoClosingBrackets": "always", "editor.autoClosingQuotes": "always", "editor.colorDecorators": true, "editor.insertSpaces": true, "editor.wordWrap": "wordWrapColumn", "editor.wordWrapColumn": 140, "editor.formatOnPaste": true, "editor.formatOnSave": true, "files.trimFinalNewlines": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}}
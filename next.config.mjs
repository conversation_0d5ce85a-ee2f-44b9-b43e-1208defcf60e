/** @type {import('next').NextConfig} */

const nextConfig = {
    reactStrictMode: false,
    assetPrefix: "/assets-crm",
    swcMinify: false,
    basePath: "/assets-crm",
    staticPageGenerationTimeout: 1000,
    compiler: {
        styledComponents: true,
    },
    images: {
        remotePatterns: [
            {
                protocol: "https",
                hostname: "**",
            },
            {
                protocol: "http",
                hostname: "**",
            },
        ],
    },
};

export default nextConfig;

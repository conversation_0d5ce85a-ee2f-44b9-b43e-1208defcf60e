stages:
  - build
  - deploy


variables:
  DOCKER_FILE_PATH: "./"
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""

.dependency-setup:
  before_script:
    - |
      apk update
      apk add --no-cache python3 py3-pip
      pip3 install awscli --break-system-packages
    - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
    - export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
    - export AWS_DEFAULT_REGION=$AWS_DEFAULT_REGION


build-job:
  extends: .dependency-setup
  stage: build
  environment: dev
  script:
    - docker build $DOCKER_FILE_PATH -t $DOCKER_IMAGE
    - aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 722386258707.dkr.ecr.ap-south-1.amazonaws.com
    - docker push $DOCKER_IMAGE
    - docker rmi $DOCKER_IMAGE
  only:
    refs:
      - development
  tags:
    - kgk-dev

deployment-job:
  stage: deploy
  environment: dev
  script:
    - |
      echo "Deploying to Kubernetes..."
      sleep 10
      export DEPLOY=$DEPLOY_NAME
      echo "Deployment name: $DEPLOY"
      eval $(ssh-agent -s)
      echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null  # Add the SSH key      
      
      ssh -o StrictHostKeyChecking=no ubuntu@************** /bin/bash << EOF
        export DEPLOY=$DEPLOY
        if [[ -z "\$DEPLOY" ]]; then
          echo "Error: DEPLOY variable is not set. Aborting deployment."
          exit 1
        fi
        echo "Patching deployment: \$DEPLOY"
        kubectl patch deployment \$DEPLOY --patch "{\"spec\": {\"template\": {\"metadata\": {\"annotations\": {\"date\": \"$(date '+%Y%m%d%H%M%S')\"}}}}}"
        sleep 5
        kubectl rollout status deployment/\$DEPLOY --timeout=600s
        if [[ "\$?" -ne 0 ]]; then
          echo -e "\n\$DEPLOY deployment failed! Please see the environment";
          exit 1;
        else
          echo -e "\n\$DEPLOY pods are upgraded to latest version of staging and in running state now";
        fi
      EOF
  only:
    refs:
      - development
  tags:
    - kgk-dev
  needs:
    - build-job

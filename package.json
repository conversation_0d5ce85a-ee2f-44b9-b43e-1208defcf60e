{"name": "asset_crm", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "start": "next start -p 3015", "lint": "next lint", "eject": "react-scripts eject", "lint:fix": "eslint . --ext .ts,.tsx --fix", "build": "next build", "clean": "rimraf .next && rimraf node_modules"}, "overrides": {"typescript": "^5.2.2"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.0", "@babel/plugin-syntax-flow": "^7.22.5", "@dilpesh/kgk-ui-library": "2.7.31", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/eb-garamond": "^5.0.13", "@fontsource/inter": "^5.0.13", "@magneto-it-solutions/kgk-common-library": "1.4.97", "@mui/material": "^5.14.13", "@reduxjs/toolkit": "^1.9.7", "@types/lodash": "^4.14.195", "@types/node": "^20.3.3", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/styled-components": "^5.1.29", "antd": "^5.9.4", "axios": "^1.4.0", "chart.js": "^4.4.0", "cookies": "^0.8.0", "cookies-next": "^4.0.0", "file-loader": "^6.2.0", "form-data": "^4.0.0", "immer": "^10.0.2", "js-sha256": "^0.9.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "next": "14.0.2", "next-react-svg": "^1.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-gtm-module": "^2.0.11", "react-hook-form": "^7.45.1", "react-phone-input-2": "^2.15.1", "react-redux": "^8.1.1", "react-scripts": "^5.0.1", "react-svg": "^16.1.28", "redux": "^4.2.1", "redux-logger": "^3.0.6", "sass": "^1.77.6", "styled-components": "^5.3.1", "swiper": "^10.3.1", "typescript": "^5.2.2", "web-vitals": "^2.1.4"}, "devDependencies": {"@next/eslint-plugin-next": "^14.0.3", "@svgr/webpack": "^8.1.0", "@types/node": "^20.3.3", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/react-redux": "^7.1.25", "@types/react-slick": "^0.23.10", "@types/redux-logger": "^3.0.9", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "encoding": "^0.1.13", "eslint": "^8.51.0", "eslint-config-next": "14.0.2", "eslint-plugin-react-hooks": "^4.6.0", "identity-obj-proxy": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-is": "^18.2.0", "react-slick": "^0.29.0", "rimraf": "^5.0.1", "typescript": "^5.2.2", "webpack": "^5.88.0", "webpack-cli": "^5.1.4"}}